#!/usr/bin/env python3
"""
快速设置脚本
"""

import os
import sys
from pathlib import Path

def main():
    print("🎨 卓建图像生成服务 - 快速设置")
    print("=" * 40)
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ 未找到.env文件，请先运行: uv run python config_helper.py --create-env")
        return False
    
    # 读取当前API密钥
    with open(env_file, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 查找API密钥行
    lines = content.split('\n')
    api_key_line_index = -1
    current_key = ""
    
    for i, line in enumerate(lines):
        if line.startswith('GEMINI_API_KEY='):
            api_key_line_index = i
            current_key = line.split('=', 1)[1].strip()
            break
    
    if api_key_line_index == -1:
        print("❌ 未找到GEMINI_API_KEY配置行")
        return False
    
    # 检查是否需要配置
    if current_key and current_key != "your-api-key-here":
        print(f"✅ 已配置API密钥: {current_key[:10]}...")
        return True
    
    print("⚠️ API密钥未配置")
    print("\n📋 获取API密钥步骤：")
    print("1. 访问 https://aihubmix.com")
    print("2. 注册账号并登录")
    print("3. 在控制台生成API密钥")
    print("4. 复制密钥（通常以sk-开头）")
    
    # 输入API密钥
    while True:
        api_key = input("\n🔑 请输入您的API密钥: ").strip()
        
        if not api_key:
            print("❌ API密钥不能为空")
            continue
        
        # 更新配置文件
        lines[api_key_line_index] = f"GEMINI_API_KEY={api_key}"
        new_content = '\n'.join(lines)
        
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print("✅ API密钥已保存")
        return True

if __name__ == "__main__":
    if main():
        print("\n🚀 配置完成！现在可以启动服务：")
        print("  python run.py")
        
        # 询问是否立即启动
        start = input("\n是否立即启动服务？(Y/n): ").strip().lower()
        if start != 'n':
            print("\n🚀 启动服务...")
            os.system("python run.py")
    else:
        print("\n❌ 配置失败")
        sys.exit(1)
