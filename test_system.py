#!/usr/bin/env python3
"""
系统测试脚本
测试API服务器和图像生成功能
"""

import requests
import time
import json
from pathlib import Path

# 配置
API_BASE_URL = "http://localhost:8000"
TEST_PROMPT = "A simple geometric logo with a blue circle and white text 'TEST' inside"

def test_api_health():
    """测试API健康状态"""
    print("🔍 测试API健康状态...")
    try:
        response = requests.get(f"{API_BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ API服务器运行正常")
            return True
        else:
            print(f"❌ API服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保服务器已启动")
        return False
    except Exception as e:
        print(f"❌ API健康检查失败: {e}")
        return False

def test_image_generation():
    """测试图像生成功能"""
    print("🎨 测试图像生成功能...")
    
    # 准备请求数据
    request_data = {
        "prompt": TEST_PROMPT,
        "model": "imagen-3.0-generate-002",
        "aspect_ratio": "1:1",
        "number_of_images": 1
    }
    
    try:
        # 发送生成请求
        print("📤 发送图像生成请求...")
        response = requests.post(
            f"{API_BASE_URL}/generate",
            json=request_data,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ 生成请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        result = response.json()
        if not result.get("success"):
            print(f"❌ 生成请求被拒绝: {result.get('message')}")
            return False
        
        task_id = result.get("task_id")
        print(f"✅ 生成任务已创建: {task_id}")
        
        # 轮询任务状态
        print("⏳ 等待图像生成完成...")
        max_attempts = 30  # 最多等待30次，每次2秒
        
        for attempt in range(max_attempts):
            time.sleep(2)
            
            status_response = requests.get(f"{API_BASE_URL}/task/{task_id}")
            if status_response.status_code != 200:
                print(f"❌ 获取任务状态失败: {status_response.status_code}")
                return False
            
            status_data = status_response.json()
            status = status_data.get("status")
            progress = status_data.get("progress", 0)
            
            print(f"📊 进度: {progress}% - 状态: {status}")
            
            if status == "completed":
                result_data = status_data.get("result", {})
                if result_data.get("success"):
                    images = result_data.get("images", [])
                    print(f"✅ 图像生成成功！生成了 {len(images)} 张图像")
                    
                    # 测试图像下载
                    for i, image in enumerate(images):
                        image_url = f"{API_BASE_URL}{image['url']}"
                        print(f"🖼️ 图像 {i+1}: {image_url}")
                        
                        # 尝试下载图像
                        img_response = requests.get(image_url)
                        if img_response.status_code == 200:
                            print(f"✅ 图像 {i+1} 可以正常访问")
                        else:
                            print(f"❌ 图像 {i+1} 访问失败: {img_response.status_code}")
                    
                    return True
                else:
                    print(f"❌ 图像生成失败: {result_data.get('message')}")
                    return False
            
            elif status == "failed":
                result_data = status_data.get("result", {})
                print(f"❌ 图像生成失败: {result_data.get('message')}")
                return False
            
            elif status in ["pending", "processing"]:
                continue  # 继续等待
            
            else:
                print(f"❌ 未知任务状态: {status}")
                return False
        
        print("❌ 图像生成超时")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 图像生成测试失败: {e}")
        return False

def test_task_management():
    """测试任务管理功能"""
    print("📋 测试任务管理功能...")
    
    try:
        # 获取任务列表
        response = requests.get(f"{API_BASE_URL}/tasks")
        if response.status_code == 200:
            tasks = response.json().get("tasks", {})
            print(f"✅ 任务列表获取成功，当前有 {len(tasks)} 个任务")
            return True
        else:
            print(f"❌ 获取任务列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 任务管理测试失败: {e}")
        return False

def test_web_interface():
    """测试Web界面"""
    print("🌐 测试Web界面...")
    
    try:
        response = requests.get(API_BASE_URL)
        if response.status_code == 200 and "卓建图像生成" in response.text:
            print("✅ Web界面可以正常访问")
            return True
        else:
            print(f"❌ Web界面访问异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*60)
    print("🧪 卓建图像生成系统测试报告")
    print("="*60)
    
    tests = [
        ("API健康检查", test_api_health),
        ("Web界面测试", test_web_interface),
        ("任务管理测试", test_task_management),
        ("图像生成测试", test_image_generation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查系统配置")
        return False

def main():
    """主函数"""
    print("🚀 开始系统测试...")
    
    # 检查输出目录
    output_dir = Path("output")
    if not output_dir.exists():
        output_dir.mkdir()
        print("📁 创建输出目录")
    
    # 运行测试
    success = generate_test_report()
    
    if success:
        print("\n🌟 系统测试完成！")
        print("💡 提示:")
        print("  - Web界面: http://localhost:8000")
        print("  - API文档: http://localhost:8000/docs")
        print("  - 生成的图像保存在 output/ 目录中")
    else:
        print("\n⚠️ 系统测试发现问题，请检查配置和服务状态")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
