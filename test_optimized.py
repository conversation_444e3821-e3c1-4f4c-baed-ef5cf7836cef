#!/usr/bin/env python3
"""
优化后系统测试脚本
"""

import requests
import time
import json

API_BASE = "http://localhost:8000/api"

def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点...")
    
    # 测试统计信息
    try:
        response = requests.get(f"{API_BASE}/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 统计信息: {stats}")
        else:
            print(f"❌ 统计信息获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 统计信息测试失败: {e}")
    
    # 测试任务列表
    try:
        response = requests.get(f"{API_BASE}/tasks")
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 任务列表: {tasks['total_tasks']} 个任务")
        else:
            print(f"❌ 任务列表获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 任务列表测试失败: {e}")

def test_image_generation():
    """测试图像生成"""
    print("🎨 测试图像生成...")
    
    request_data = {
        "prompt": "A simple test image with blue background",
        "model": "imagen-3.0-generate-002",
        "aspect_ratio": "1:1",
        "number_of_images": 1
    }
    
    try:
        # 发送生成请求
        response = requests.post(f"{API_BASE}/generate", json=request_data)
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"✅ 任务创建成功: {task_id}")
            
            # 等待完成
            for i in range(30):
                time.sleep(2)
                status_response = requests.get(f"{API_BASE}/task/{task_id}")
                if status_response.status_code == 200:
                    status = status_response.json()
                    print(f"📊 进度: {status['progress']}% - {status['status']}")
                    
                    if status['status'] == 'completed':
                        print("✅ 图像生成完成!")
                        return True
                    elif status['status'] == 'failed':
                        print(f"❌ 生成失败: {status.get('message')}")
                        return False
            
            print("❌ 生成超时")
            return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 生成测试失败: {e}")
        return False

def main():
    print("🚀 优化后系统测试")
    print("=" * 40)
    
    test_api_endpoints()
    print()
    test_image_generation()
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main()
