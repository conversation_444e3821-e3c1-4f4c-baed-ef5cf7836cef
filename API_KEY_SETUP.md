# 🔑 API密钥配置指南

## 问题描述

如果您看到以下错误信息：
```
图像生成失败: 401 None. {'error': {'message': '获取Token失败', 'type': 'Aihubmix_api_error'}, 'type': 'error'}
```

这表明API密钥配置有问题。请按照以下步骤解决：

## 🚀 解决方案

### 步骤1：获取API密钥

1. **访问AiHubMix官网**
   - 打开 [https://aihubmix.com](https://aihubmix.com)
   - 注册账号并登录

2. **生成API密钥**
   - 进入控制台/仪表板
   - 找到API密钥管理页面
   - 创建新的API密钥
   - 复制生成的密钥（通常以`sk-`开头）

### 步骤2：配置API密钥

#### 方法1：编辑.env文件（推荐）

1. **打开项目目录下的`.env`文件**
   ```bash
   # 使用文本编辑器打开
   nano .env
   # 或者
   code .env
   ```

2. **修改API密钥配置**
   ```bash
   # 将这行：
   GEMINI_API_KEY=your-api-key-here
   
   # 改为：
   GEMINI_API_KEY=sk-your-actual-api-key-here
   ```

3. **保存文件**

#### 方法2：设置环境变量

**Linux/macOS:**
```bash
export GEMINI_API_KEY=sk-your-actual-api-key-here
```

**Windows:**
```cmd
set GEMINI_API_KEY=sk-your-actual-api-key-here
```

### 步骤3：重启服务

配置完成后，重启服务器：
```bash
# 停止当前服务（Ctrl+C）
# 然后重新启动
python run.py
```

## 🔍 验证配置

### 使用配置助手

运行配置检查工具：
```bash
uv run python config_helper.py --check
```

### 手动验证

1. **检查.env文件**
   ```bash
   cat .env | grep GEMINI_API_KEY
   ```

2. **测试API连接**
   ```bash
   uv run python config_helper.py --test
   ```

## 🛠️ 故障排除

### 常见问题

1. **API密钥格式错误**
   - 确保密钥以`sk-`开头
   - 检查是否有多余的空格或换行符

2. **环境变量未生效**
   - 重启终端/命令行
   - 确认.env文件在项目根目录

3. **API密钥无效**
   - 检查密钥是否过期
   - 确认账户余额是否充足
   - 重新生成新的API密钥

4. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置
   - 尝试使用VPN

### 错误代码说明

- **401 Unauthorized**: API密钥无效或未配置
- **403 Forbidden**: API密钥权限不足
- **429 Too Many Requests**: 请求频率过高
- **500 Internal Server Error**: 服务器内部错误

## 📞 获取帮助

如果问题仍然存在：

1. **检查AiHubMix官方文档**
   - 访问官方文档获取最新信息

2. **联系技术支持**
   - 通过AiHubMix官网联系客服

3. **查看日志**
   ```bash
   # 查看详细错误日志
   python run.py --reload
   ```

## ✅ 配置成功标志

当配置正确时，您应该看到：

1. **启动日志显示**：
   ```
   ✅ API密钥已配置
   ✅ Google GenAI客户端初始化成功
   ```

2. **Web界面显示**：
   - 配置警告消失
   - 生成按钮可用
   - 服务状态显示"已配置"

3. **能够成功生成图像**

## 🔒 安全提醒

- ❌ 不要将API密钥提交到版本控制系统
- ❌ 不要在公开场所分享API密钥
- ✅ 定期更换API密钥
- ✅ 监控API使用情况
