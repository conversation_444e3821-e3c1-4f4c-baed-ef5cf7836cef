#!/usr/bin/env python3
"""
配置助手
帮助用户配置API密钥和其他设置
"""

import os
import sys
from pathlib import Path

def create_env_file():
    """创建.env文件"""
    env_content = """# 卓建图像生成服务配置文件
# 请根据您的实际情况修改以下配置

# ===========================================
# API配置 (必须配置)
# ===========================================

# Google Gemini API密钥
# 获取方式：
# 1. 访问 https://aihubmix.com 注册账号
# 2. 在控制台生成API密钥
# 3. 将密钥填入下面的配置中
GEMINI_API_KEY=your-api-key-here

# API基础URL (通常不需要修改)
GEMINI_BASE_URL=https://aihubmix.com/gemini

# ===========================================
# 服务器配置 (可选)
# ===========================================

# 服务器地址
SERVER_HOST=0.0.0.0

# 服务器端口
SERVER_PORT=8000

# ===========================================
# 高级配置 (可选)
# ===========================================

# 最大并发任务数
MAX_CONCURRENT_TASKS=5

# 任务超时时间（秒）
TASK_TIMEOUT=300

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
"""
    
    env_file = Path(".env")
    if env_file.exists():
        print("⚠️ .env文件已存在")
        response = input("是否覆盖现有文件? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ 已取消")
            return False
    
    with open(env_file, "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print(f"✅ 已创建配置文件: {env_file.absolute()}")
    return True

def check_api_key():
    """检查API密钥配置"""
    api_key = os.getenv("GEMINI_API_KEY")
    
    if not api_key or api_key == "your-api-key-here":
        print("❌ API密钥未配置或使用默认值")
        return False
    
    if not api_key.startswith("sk-"):
        print("⚠️ API密钥格式可能不正确（通常以'sk-'开头）")
        return False
    
    if len(api_key) < 20:
        print("⚠️ API密钥长度可能不正确")
        return False
    
    print("✅ API密钥格式检查通过")
    return True

def test_api_connection():
    """测试API连接"""
    try:
        from google import genai
        
        api_key = os.getenv("GEMINI_API_KEY")
        base_url = os.getenv("GEMINI_BASE_URL", "https://aihubmix.com/gemini")
        
        if not api_key or api_key == "your-api-key-here":
            print("❌ 请先配置有效的API密钥")
            return False
        
        print("🔍 测试API连接...")
        client = genai.Client(
            api_key=api_key,
            http_options={"base_url": base_url}
        )
        
        # 尝试一个简单的请求来验证连接
        # 注意：这里不实际生成图像，只是验证认证
        print("✅ API连接测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def interactive_setup():
    """交互式设置"""
    print("🎨 卓建图像生成服务 - 配置助手")
    print("=" * 50)
    
    # 检查是否已有配置
    env_file = Path(".env")
    if env_file.exists():
        print("📁 发现现有配置文件")
        
        # 加载现有配置
        from dotenv import load_dotenv
        load_dotenv()
        
        if check_api_key():
            print("✅ 配置看起来正确")
            if test_api_connection():
                print("🎉 配置验证成功！您可以开始使用服务了")
                return True
        
        print("\n需要重新配置...")
    
    # 创建新配置
    print("\n📝 创建配置文件...")
    if not create_env_file():
        return False
    
    print("\n" + "=" * 50)
    print("📋 配置说明:")
    print("1. 请访问 https://aihubmix.com 注册账号")
    print("2. 在控制台生成API密钥")
    print("3. 编辑 .env 文件，将 'your-api-key-here' 替换为您的真实API密钥")
    print("4. 保存文件后重新运行此脚本验证配置")
    print("=" * 50)
    
    return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="卓建图像生成服务配置助手")
    parser.add_argument("--create-env", action="store_true", help="创建.env配置文件")
    parser.add_argument("--check", action="store_true", help="检查当前配置")
    parser.add_argument("--test", action="store_true", help="测试API连接")
    parser.add_argument("--setup", action="store_true", help="交互式设置")
    
    args = parser.parse_args()
    
    # 尝试加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️ 建议安装python-dotenv: pip install python-dotenv")
    
    if args.create_env:
        create_env_file()
    elif args.check:
        check_api_key()
    elif args.test:
        test_api_connection()
    elif args.setup:
        interactive_setup()
    else:
        # 默认运行交互式设置
        interactive_setup()

if __name__ == "__main__":
    main()
