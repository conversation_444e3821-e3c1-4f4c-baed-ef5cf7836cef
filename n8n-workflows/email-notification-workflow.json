{"name": "图像生成邮件通知流程", "nodes": [{"parameters": {"httpMethod": "POST", "path": "email-notify", "responseMode": "responseNode", "options": {}}, "id": "email-webhook", "name": "邮件通知触发器", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "email-notification-webhook"}, {"parameters": {"url": "http://localhost:8000/generate", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.request }}", "options": {"timeout": 30000}}, "id": "generate-image", "name": "生成图像", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300], "alwaysOutputData": true}, {"parameters": {"amount": 10, "unit": "seconds"}, "id": "wait-generation", "name": "等待生成完成", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "=http://localhost:8000/task/{{ $('生成图像').item.json.task_id }}", "options": {"timeout": 10000}}, "id": "check-result", "name": "检查生成结果", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300], "alwaysOutputData": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "success-condition", "leftValue": "={{ $json.status }}", "rightValue": "completed", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-success", "name": "检查是否成功", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "service": "gmail", "fromEmail": "<EMAIL>", "toEmail": "={{ $('邮件通知触发器').item.json.email }}", "subject": "🎨 图像生成完成通知", "message": "=您好！\n\n您请求的图像已经生成完成。\n\n生成详情：\n- 提示词：{{ $('邮件通知触发器').item.json.request.prompt }}\n- 模型：{{ $('邮件通知触发器').item.json.request.model }}\n- 宽高比：{{ $('邮件通知触发器').item.json.request.aspect_ratio }}\n- 图像数量：{{ $('邮件通知触发器').item.json.request.number_of_images }}\n- 生成时间：{{ $now.format('YYYY-MM-DD HH:mm:ss') }}\n\n生成结果：{{ $json.result.message }}\n\n您可以通过以下链接查看和下载图像：\n{% for image in $json.result.images %}\n- 图像{{ loop.index }}：http://localhost:8000{{ image.url }}\n{% endfor %}\n\n感谢使用卓建图像生成工具！", "options": {"allowUnauthorizedCerts": false, "ccEmail": "", "bccEmail": ""}}, "id": "send-success-email", "name": "发送成功邮件", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1340, 200], "credentials": {"httpBasicAuth": {"id": "email-credentials", "name": "Email Credentials"}}}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "service": "gmail", "fromEmail": "<EMAIL>", "toEmail": "={{ $('邮件通知触发器').item.json.email }}", "subject": "❌ 图像生成失败通知", "message": "=您好！\n\n很抱歉，您请求的图像生成失败了。\n\n生成详情：\n- 提示词：{{ $('邮件通知触发器').item.json.request.prompt }}\n- 模型：{{ $('邮件通知触发器').item.json.request.model }}\n- 尝试时间：{{ $now.format('YYYY-MM-DD HH:mm:ss') }}\n\n失败原因：{{ $json.result?.message || '未知错误' }}\n\n请检查您的提示词或稍后重试。如果问题持续存在，请联系技术支持。\n\n感谢使用卓建图像生成工具！", "options": {"allowUnauthorizedCerts": false, "ccEmail": "", "bccEmail": ""}}, "id": "send-failure-email", "name": "发送失败邮件", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1340, 400], "credentials": {"httpBasicAuth": {"id": "email-credentials", "name": "Email Credentials"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"邮件通知已发送\",\n  \"email\": \"{{ $('邮件通知触发器').item.json.email }}\",\n  \"timestamp\": \"{{ $now.format('YYYY-MM-DD HH:mm:ss') }}\"\n}"}, "id": "success-response", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"失败通知邮件已发送\",\n  \"email\": \"{{ $('邮件通知触发器').item.json.email }}\",\n  \"timestamp\": \"{{ $now.format('YYYY-MM-DD HH:mm:ss') }}\"\n}"}, "id": "failure-response", "name": "失败响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 400]}], "connections": {"邮件通知触发器": {"main": [[{"node": "生成图像", "type": "main", "index": 0}]]}, "生成图像": {"main": [[{"node": "等待生成完成", "type": "main", "index": 0}]]}, "等待生成完成": {"main": [[{"node": "检查生成结果", "type": "main", "index": 0}]]}, "检查生成结果": {"main": [[{"node": "检查是否成功", "type": "main", "index": 0}]]}, "检查是否成功": {"main": [[{"node": "发送成功邮件", "type": "main", "index": 0}], [{"node": "发送失败邮件", "type": "main", "index": 0}]]}, "发送成功邮件": {"main": [[{"node": "成功响应", "type": "main", "index": 0}]]}, "发送失败邮件": {"main": [[{"node": "失败响应", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-08T05:00:00.000Z", "updatedAt": "2025-01-08T05:00:00.000Z", "id": "email-notification", "name": "邮件通知"}], "triggerCount": 0, "updatedAt": "2025-01-08T05:00:00.000Z", "versionId": "1"}