{"name": "卓建图像生成自动化流程", "nodes": [{"parameters": {"httpMethod": "POST", "path": "webhook-generate", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook触发器", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "image-generation-webhook"}, {"parameters": {"url": "http://localhost:8000/generate", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {"timeout": 30000}}, "id": "api-call-generate", "name": "调用图像生成API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300], "alwaysOutputData": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "success-condition", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-success", "name": "检查API调用结果", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"amount": 5, "unit": "seconds"}, "id": "wait-initial", "name": "等待初始化", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"url": "=http://localhost:8000/task/{{ $('调用图像生成API').item.json.task_id }}", "options": {"timeout": 10000}}, "id": "check-status", "name": "检查任务状态", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200], "alwaysOutputData": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "completed-condition", "leftValue": "={{ $json.status }}", "rightValue": "completed", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-completed", "name": "检查是否完成", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 200]}, {"parameters": {"amount": 3, "unit": "seconds"}, "id": "wait-retry", "name": "等待重试", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1340, 400]}, {"parameters": {"jsCode": "// 处理生成结果\nconst result = $input.first().json.result;\nconst taskId = $('调用图像生成API').item.json.task_id;\n\nif (result && result.success && result.images) {\n  const processedImages = result.images.map((image, index) => ({\n    filename: image.filename,\n    url: `http://localhost:8000${image.url}`,\n    downloadUrl: `http://localhost:8000${image.url}`,\n    base64: image.base64,\n    index: index + 1\n  }));\n  \n  return {\n    success: true,\n    message: result.message,\n    taskId: taskId,\n    images: processedImages,\n    totalImages: processedImages.length,\n    timestamp: new Date().toISOString()\n  };\n} else {\n  return {\n    success: false,\n    message: result?.message || '图像生成失败',\n    taskId: taskId,\n    images: [],\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "process-result", "name": "处理生成结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 100]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "success-response", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 100]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"message\": \"{{ $('调用图像生成API').item.json.message || '图像生成失败' }}\",\n  \"error\": true\n}", "options": {"responseCode": 400}}, "id": "error-response", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "failed-condition", "leftValue": "={{ $json.status }}", "rightValue": "failed", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-failed", "name": "检查是否失败", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"message\": \"{{ $json.result.message || '任务执行失败' }}\",\n  \"taskId\": \"{{ $('调用图像生成API').item.json.task_id }}\",\n  \"error\": true\n}", "options": {"responseCode": 500}}, "id": "failed-response", "name": "失败响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 400]}], "connections": {"Webhook触发器": {"main": [[{"node": "调用图像生成API", "type": "main", "index": 0}]]}, "调用图像生成API": {"main": [[{"node": "检查API调用结果", "type": "main", "index": 0}]]}, "检查API调用结果": {"main": [[{"node": "等待初始化", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "等待初始化": {"main": [[{"node": "检查任务状态", "type": "main", "index": 0}]]}, "检查任务状态": {"main": [[{"node": "检查是否完成", "type": "main", "index": 0}]]}, "检查是否完成": {"main": [[{"node": "处理生成结果", "type": "main", "index": 0}], [{"node": "检查是否失败", "type": "main", "index": 0}]]}, "等待重试": {"main": [[{"node": "检查任务状态", "type": "main", "index": 0}]]}, "处理生成结果": {"main": [[{"node": "成功响应", "type": "main", "index": 0}]]}, "检查是否失败": {"main": [[{"node": "失败响应", "type": "main", "index": 0}], [{"node": "等待重试", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-08T05:00:00.000Z", "updatedAt": "2025-01-08T05:00:00.000Z", "id": "image-generation", "name": "图像生成"}], "triggerCount": 0, "updatedAt": "2025-01-08T05:00:00.000Z", "versionId": "1"}