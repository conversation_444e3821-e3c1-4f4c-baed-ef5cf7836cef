#!/usr/bin/env python3
"""
卓建图像生成服务启动脚本
简化版本，专注于FastAPI + HTML
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    try:
        import fastapi
        import uvicorn
        import google.genai
        import PIL
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: uv sync")
        return False

def setup_environment():
    """设置环境"""
    # 创建必要的目录
    directories = ["output", "static", "templates"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"📁 确保目录存在: {dir_name}")
    
    # 检查环境变量
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("⚠️ 未设置 GEMINI_API_KEY 环境变量，将使用默认配置")
    else:
        print("✅ API密钥已配置")

def main():
    parser = argparse.ArgumentParser(description="卓建图像生成服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务器地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")
    parser.add_argument("--check-only", action="store_true", help="仅检查环境")
    
    args = parser.parse_args()
    
    print("🎨 卓建图像生成服务")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置环境
    setup_environment()
    
    if args.check_only:
        print("✅ 环境检查完成")
        sys.exit(0)
    
    # 启动服务
    print(f"\n🚀 启动服务...")
    print(f"📡 地址: http://{args.host}:{args.port}")
    print(f"📚 API文档: http://{args.host}:{args.port}/api/docs")
    print(f"🎨 Web界面: http://{args.host}:{args.port}")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 使用uv运行以确保正确的环境
        cmd = [
            "uv", "run", "python", "api_server.py",
            "--host", args.host,
            "--port", str(args.port)
        ]
        
        if args.reload:
            cmd.append("--reload")
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
