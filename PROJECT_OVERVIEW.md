# 🎨 卓建图像生成服务 - 项目概览

## 📋 项目简介

这是一个基于**FastAPI + HTML**的现代化图像生成服务，使用Google Gemini Imagen API提供高质量的AI图像生成功能。

### 🎯 核心特性

- **🌐 Web界面**: 现代化响应式HTML界面
- **🔌 RESTful API**: 完整的API服务
- **🎨 AI图像生成**: 支持Imagen 3.0和4.0模型
- **📱 响应式设计**: 完美适配各种设备
- **⚡ 异步处理**: 高性能的后台任务处理
- **🔧 配置管理**: 智能的配置检查和修复工具

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTML前端界面   │───▶│   FastAPI服务   │───▶│  Google Gemini  │
│   (用户交互)    │    │   (API处理)     │    │   (图像生成)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   实时监控      │    │   文件存储      │
│   (状态显示)    │    │   (图像保存)    │
└─────────────────┘    └─────────────────┘
```

## 📁 核心文件说明

### 🚀 服务核心
- **`api_server.py`**: FastAPI服务器主文件，包含所有API端点和业务逻辑
- **`run.py`**: 服务启动脚本，提供环境检查和启动功能

### 🎨 前端界面
- **`static/index.html`**: 完整的Web前端界面，包含图像生成表单、进度显示、结果展示等功能

### 🔧 工具脚本
- **`config_helper.py`**: 配置助手，提供交互式配置向导
- **`fix_api_key.py`**: API密钥快速修复工具
- **`test_optimized.py`**: 系统功能测试脚本

### 📄 配置文件
- **`.env`**: 环境变量配置文件
- **`pyproject.toml`**: 项目配置和依赖管理

## 🚀 快速开始

### 1. 安装依赖
```bash
uv sync
```

### 2. 配置API密钥
```bash
uv run python fix_api_key.py
```

### 3. 启动服务
```bash
python run.py
```

### 4. 访问服务
- Web界面: http://localhost:8000
- API文档: http://localhost:8000/api/docs

## 🔌 API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | Web主页 |
| `/api/generate` | POST | 生成图像 |
| `/api/task/{task_id}` | GET | 查询任务状态 |
| `/api/tasks` | GET | 列出所有任务 |
| `/api/stats` | GET | 服务统计信息 |
| `/api/config/check` | GET | 检查配置状态 |
| `/images/{filename}` | GET | 获取图像文件 |
| `/download/{filename}` | GET | 下载图像文件 |

## 🎨 功能特性

### Web界面功能
- ✅ 直观的图像生成表单
- ✅ 实时进度显示
- ✅ 多种模型选择（Imagen 3.0/4.0）
- ✅ 多种宽高比支持（1:1, 16:9, 9:16, 4:3, 3:4）
- ✅ 批量图像生成（1-4张）
- ✅ 图像完整显示（不裁剪）
- ✅ 大图查看模态框
- ✅ 服务状态监控
- ✅ 配置问题自动检测

### API服务功能
- ✅ 异步任务处理
- ✅ 任务状态管理
- ✅ 错误处理和日志
- ✅ 配置验证
- ✅ 文件服务
- ✅ 自动API文档

### 配置管理
- ✅ 环境变量支持
- ✅ 配置文件管理
- ✅ 自动配置检查
- ✅ 快速修复工具
- ✅ 交互式配置向导

## 🛠️ 技术栈

### 后端技术
- **FastAPI**: 现代化的Python Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化
- **Google GenAI**: Google Gemini API客户端
- **Pillow**: 图像处理库

### 前端技术
- **HTML5**: 现代化的标记语言
- **CSS3**: 响应式样式设计
- **JavaScript**: 交互功能和API调用
- **Fetch API**: 异步HTTP请求

### 开发工具
- **uv**: 现代化的Python包管理器
- **python-dotenv**: 环境变量管理
- **Jinja2**: 模板引擎

## 🔒 安全特性

- ✅ API密钥安全存储
- ✅ 环境变量隔离
- ✅ 输入验证和清理
- ✅ 错误信息过滤
- ✅ CORS配置

## 📈 性能特性

- ✅ 异步处理
- ✅ 并发控制
- ✅ 任务队列管理
- ✅ 文件缓存
- ✅ 响应式设计

## 🎯 项目优势

1. **简洁架构**: 专注于FastAPI + HTML，无复杂依赖
2. **用户友好**: 完善的配置工具和错误提示
3. **功能完整**: 从图像生成到文件管理的完整流程
4. **易于部署**: 单一服务，简单配置
5. **可扩展性**: 清晰的代码结构，易于扩展功能

## 🔮 未来规划

- 🔄 支持更多AI模型
- 📊 使用统计和分析
- 🎨 图像编辑功能
- 🔔 通知系统
- 🌐 多语言支持

这是一个专注、高效、用户友好的图像生成服务！🎉
