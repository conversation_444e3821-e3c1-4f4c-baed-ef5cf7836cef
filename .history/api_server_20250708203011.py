#!/usr/bin/env python3
"""
卓建图像生成服务器
基于FastAPI的高性能图像生成服务
"""

import os
import time
import base64
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel, Field, validator
from google import genai
from google.genai import types
from PIL import Image
from io import BytesIO
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
import json
from pathlib import Path

# 创建FastAPI应用
app = FastAPI(
    title="🎨 卓建图像生成服务",
    description="基于Google Gemini Imagen的智能图像生成平台",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
class Config:
    API_KEY = os.getenv("GEMINI_API_KEY", "sk-sSS1IGDDwqLWnTzY3aB4B4184f0141E19fCe2a272599DcDd")
    BASE_URL = os.getenv("GEMINI_BASE_URL", "https://aihubmix.com/gemini")
    OUTPUT_DIR = Path("output")
    STATIC_DIR = Path("static")
    TEMPLATES_DIR = Path("templates")
    MAX_CONCURRENT_TASKS = 5
    TASK_TIMEOUT = 300  # 5分钟

    def __init__(self):
        # 确保目录存在
        self.OUTPUT_DIR.mkdir(exist_ok=True)
        self.STATIC_DIR.mkdir(exist_ok=True)
        self.TEMPLATES_DIR.mkdir(exist_ok=True)

config = Config()

# 模板引擎
templates = Jinja2Templates(directory=str(config.TEMPLATES_DIR))

# 数据模型
class ImageGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=1000, description="图像描述提示词")
    model: str = Field(default="imagen-3.0-generate-002", description="生成模型")
    number_of_images: int = Field(default=1, ge=1, le=4, description="生成图像数量")
    aspect_ratio: str = Field(default="1:1", description="图像宽高比")

    @validator('model')
    def validate_model(cls, v):
        allowed_models = ["imagen-3.0-generate-002", "imagen-4.0-generate-preview-05-20"]
        if v not in allowed_models:
            raise ValueError(f"模型必须是以下之一: {allowed_models}")
        return v

    @validator('aspect_ratio')
    def validate_aspect_ratio(cls, v):
        allowed_ratios = ["1:1", "9:16", "16:9", "3:4", "4:3"]
        if v not in allowed_ratios:
            raise ValueError(f"宽高比必须是以下之一: {allowed_ratios}")
        return v

class ImageInfo(BaseModel):
    filename: str
    url: str
    download_url: str
    width: Optional[int] = None
    height: Optional[int] = None
    size_bytes: Optional[int] = None

class ImageGenerationResponse(BaseModel):
    success: bool
    message: str
    task_id: Optional[str] = None
    images: List[ImageInfo] = []
    generation_time: Optional[float] = None
    created_at: Optional[datetime] = None

class TaskStatus(BaseModel):
    task_id: str
    status: str  # "pending", "processing", "completed", "failed"
    progress: int = 0
    message: str = ""
    result: Optional[ImageGenerationResponse] = None
    created_at: datetime
    updated_at: datetime

# 任务管理器
class TaskManager:
    def __init__(self):
        self.tasks: Dict[str, TaskStatus] = {}
        self.active_tasks = 0

    def create_task(self, task_id: str) -> TaskStatus:
        now = datetime.now()
        task = TaskStatus(
            task_id=task_id,
            status="pending",
            progress=0,
            message="任务已创建",
            created_at=now,
            updated_at=now
        )
        self.tasks[task_id] = task
        return task

    def update_task(self, task_id: str, **kwargs):
        if task_id in self.tasks:
            task = self.tasks[task_id]
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)
            task.updated_at = datetime.now()

    def get_task(self, task_id: str) -> Optional[TaskStatus]:
        return self.tasks.get(task_id)

    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        to_remove = [
            task_id for task_id, task in self.tasks.items()
            if task.created_at.timestamp() < cutoff_time
        ]
        for task_id in to_remove:
            del self.tasks[task_id]
        return len(to_remove)

task_manager = TaskManager()

# 图像生成服务
class ImageGenerationService:
    def __init__(self):
        self.client = None
        self._init_client()

    def _init_client(self):
        """初始化Google GenAI客户端"""
        try:
            self.client = genai.Client(
                api_key=config.API_KEY,
                http_options={"base_url": config.BASE_URL}
            )
            logger.info("Google GenAI客户端初始化成功")
        except Exception as e:
            logger.error(f"Google GenAI客户端初始化失败: {e}")
            raise

    def save_image_from_bytes(self, image_bytes: bytes, filename: str) -> tuple[str, ImageInfo]:
        """保存图像并返回文件信息"""
        try:
            # 打开图像获取信息
            image = Image.open(BytesIO(image_bytes))
            width, height = image.size

            # 保存图像
            file_path = config.OUTPUT_DIR / filename
            image.save(file_path, optimize=True, quality=95)

            # 获取文件大小
            size_bytes = file_path.stat().st_size

            # 创建图像信息
            image_info = ImageInfo(
                filename=filename,
                url=f"/images/{filename}",
                download_url=f"/download/{filename}",
                width=width,
                height=height,
                size_bytes=size_bytes
            )

            logger.info(f"图像已保存: {filename} ({width}x{height}, {size_bytes} bytes)")
            return str(file_path), image_info

        except Exception as e:
            logger.error(f"保存图像失败: {e}")
            raise

    async def generate_images(self, request: ImageGenerationRequest, task_id: str) -> ImageGenerationResponse:
        """生成图像"""
        start_time = time.time()

        try:
            # 更新任务状态
            task_manager.update_task(task_id, status="processing", progress=10, message="开始生成图像...")

            if not self.client:
                self._init_client()

            # 更新进度
            task_manager.update_task(task_id, progress=30, message="调用AI模型...")

            # 生成图像
            response = self.client.models.generate_images(
                model=request.model,
                prompt=request.prompt,
                config=types.GenerateImagesConfig(
                    number_of_images=request.number_of_images,
                    aspect_ratio=request.aspect_ratio,
                )
            )

            # 更新进度
            task_manager.update_task(task_id, progress=70, message="处理生成结果...")

            # 保存图像
            timestamp = int(time.time())
            images = []

            for i, generated_image in enumerate(response.generated_images):
                filename = f"imagen_{timestamp}_{task_id}_{i+1}.png"
                file_path, image_info = self.save_image_from_bytes(
                    generated_image.image.image_bytes,
                    filename
                )
                images.append(image_info)

            generation_time = time.time() - start_time

            # 创建响应
            result = ImageGenerationResponse(
                success=True,
                message=f"成功生成 {len(images)} 张图像",
                task_id=task_id,
                images=images,
                generation_time=generation_time,
                created_at=datetime.now()
            )

            # 完成任务
            task_manager.update_task(
                task_id,
                status="completed",
                progress=100,
                message="图像生成完成",
                result=result
            )

            logger.info(f"任务 {task_id} 完成，生成 {len(images)} 张图像，耗时 {generation_time:.2f}s")
            return result

        except Exception as e:
            error_msg = f"图像生成失败: {str(e)}"
            logger.error(f"任务 {task_id} 失败: {error_msg}")

            # 标记任务失败
            result = ImageGenerationResponse(
                success=False,
                message=error_msg,
                task_id=task_id,
                images=[],
                created_at=datetime.now()
            )

            task_manager.update_task(
                task_id,
                status="failed",
                progress=0,
                message=error_msg,
                result=result
            )

            return result

image_service = ImageGenerationService()

# API端点
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    try:
        # 尝试使用模板
        return templates.TemplateResponse("index.html", {"request": request})
    except:
        # 降级到静态文件
        try:
            with open(config.STATIC_DIR / "index.html", "r", encoding="utf-8") as f:
                return HTMLResponse(f.read())
        except FileNotFoundError:
            return HTMLResponse("""
                <html>
                    <head><title>卓建图像生成服务</title></head>
                    <body>
                        <h1>🎨 卓建图像生成服务</h1>
                        <p>服务正在运行</p>
                        <p><a href="/api/docs">查看API文档</a></p>
                    </body>
                </html>
            """)

@app.post("/api/generate", response_model=ImageGenerationResponse)
async def generate_images_api(request: ImageGenerationRequest, background_tasks: BackgroundTasks):
    """生成图像API"""
    # 检查并发限制
    if task_manager.active_tasks >= config.MAX_CONCURRENT_TASKS:
        raise HTTPException(
            status_code=429,
            detail=f"服务繁忙，当前最多支持 {config.MAX_CONCURRENT_TASKS} 个并发任务"
        )

    # 生成任务ID
    task_id = f"task_{int(time.time())}_{hash(request.prompt) % 10000}"

    # 创建任务
    task_manager.create_task(task_id)
    task_manager.active_tasks += 1

    # 添加后台任务
    background_tasks.add_task(process_generation_task, request, task_id)

    return ImageGenerationResponse(
        success=True,
        message="图像生成任务已启动",
        task_id=task_id,
        created_at=datetime.now()
    )

async def process_generation_task(request: ImageGenerationRequest, task_id: str):
    """处理图像生成任务"""
    try:
        await image_service.generate_images(request, task_id)
    finally:
        task_manager.active_tasks = max(0, task_manager.active_tasks - 1)

@app.get("/api/task/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str):
    """获取任务状态"""
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    return task

@app.get("/", response_class=HTMLResponse)
async def root():
    """返回主页HTML"""
    try:
        with open(os.path.join(STATIC_DIR, "index.html"), "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return """
        <html>
            <body>
                <h1>卓建图像生成API</h1>
                <p>API服务正在运行</p>
                <p><a href="/docs">查看API文档</a></p>
            </body>
        </html>
        """

@app.post("/generate", response_model=ImageGenerationResponse)
async def generate_image(request: ImageGenerationRequest, background_tasks: BackgroundTasks):
    """异步生成图像"""
    # 生成任务ID
    task_id = f"task_{int(time.time())}_{hash(request.prompt) % 10000}"
    
    # 初始化任务
    tasks[task_id] = {
        "status": "pending",
        "progress": 0,
        "created_at": time.time(),
        "request": request.dict()
    }
    
    # 添加后台任务
    background_tasks.add_task(generate_images_sync, request, task_id)
    
    return ImageGenerationResponse(
        success=True,
        message="图像生成任务已启动",
        task_id=task_id
    )

@app.get("/task/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = tasks[task_id]
    return TaskStatus(
        task_id=task_id,
        status=task["status"],
        progress=task["progress"],
        result=task.get("result")
    )

@app.get("/images/{filename}")
async def get_image(filename: str):
    """获取生成的图像"""
    file_path = os.path.join(OUTPUT_DIR, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="图像不存在")
    return FileResponse(file_path)

@app.get("/tasks")
async def list_tasks():
    """列出所有任务"""
    return {"tasks": tasks}

@app.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """删除任务"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    del tasks[task_id]
    return {"message": "任务已删除"}

# 挂载静态文件
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
