#!/usr/bin/env python3
"""
API密钥快速修复工具
帮助用户快速配置API密钥
"""

import os
import sys
from pathlib import Path

def main():
    print("🔑 API密钥快速修复工具")
    print("=" * 40)
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ 未找到.env文件")
        print("请先运行: uv run python config_helper.py --create-env")
        return
    
    print("📁 找到.env文件")
    
    # 读取当前配置
    with open(env_file, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查当前API密钥
    current_key = None
    for line in content.split('\n'):
        if line.startswith('GEMINI_API_KEY='):
            current_key = line.split('=', 1)[1].strip()
            break
    
    if current_key == "your-api-key-here" or not current_key or current_key.strip() == "":
        print("⚠️ 检测到API密钥未配置")
        print("\n📋 请按照以下步骤配置API密钥：")
        print("1. 访问 https://aihubmix.com 注册账号")
        print("2. 在控制台生成API密钥")
        print("3. 输入您的API密钥（以sk-开头）")
        
        # 交互式输入API密钥
        while True:
            api_key = input("\n🔑 请输入您的API密钥: ").strip()
            
            if not api_key:
                print("❌ API密钥不能为空")
                continue
            
            if not api_key.startswith("sk-"):
                print("⚠️ API密钥通常以'sk-'开头，您确定这是正确的密钥吗？")
                confirm = input("是否继续？(y/N): ").strip().lower()
                if confirm != 'y':
                    continue
            
            if len(api_key) < 20:
                print("⚠️ API密钥长度似乎太短，您确定这是正确的密钥吗？")
                confirm = input("是否继续？(y/N): ").strip().lower()
                if confirm != 'y':
                    continue
            
            # 更新.env文件
            new_content = content.replace(
                f"GEMINI_API_KEY={current_key}",
                f"GEMINI_API_KEY={api_key}"
            )
            
            with open(env_file, "w", encoding="utf-8") as f:
                f.write(new_content)
            
            print("✅ API密钥已更新")
            break
    
    else:
        print(f"✅ 检测到已配置的API密钥: {current_key[:10]}...")
        
        # 询问是否要更新
        update = input("是否要更新API密钥？(y/N): ").strip().lower()
        if update == 'y':
            new_key = input("🔑 请输入新的API密钥: ").strip()
            if new_key:
                new_content = content.replace(
                    f"GEMINI_API_KEY={current_key}",
                    f"GEMINI_API_KEY={new_key}"
                )
                
                with open(env_file, "w", encoding="utf-8") as f:
                    f.write(new_content)
                
                print("✅ API密钥已更新")
    
    print("\n🚀 配置完成！")
    print("请重启服务器使配置生效：")
    print("  python run.py")
    
    # 询问是否立即启动服务器
    start_server = input("\n是否立即启动服务器？(Y/n): ").strip().lower()
    if start_server != 'n':
        print("\n🚀 启动服务器...")
        os.system("python run.py")

if __name__ == "__main__":
    main()
