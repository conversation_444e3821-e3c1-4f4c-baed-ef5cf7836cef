# 🎨 卓建图像生成服务

基于Google Gemini Imagen API的现代化图像生成平台，提供Web界面和RESTful API。

## ✨ 功能特性

### 🖼️ 图像生成
- 支持Imagen 3.0和4.0模型
- 多种宽高比（1:1, 9:16, 16:9, 3:4, 4:3）
- 批量生成（1-4张图像）
- 高质量图像输出

### 🌐 Web界面
- 现代化响应式设计
- 实时进度显示
- 图像预览和下载
- 大图查看模态框
- 服务状态监控

### 🔌 API服务
- RESTful API接口
- 异步任务处理
- 任务状态查询
- 自动API文档
- 错误处理和日志

## 🚀 快速开始

### 前提条件

- Python 3.12+
- uv包管理器

### 安装依赖

```bash
uv sync
```

### 启动服务

```bash
# 简单启动
python run.py

# 或者使用开发模式
python run.py --reload

# 自定义端口
python run.py --port 9000
```

## 配置

在使用前，请在`main.py`中替换API密钥：

```python
api_key="your-api-key-here"
```

## 项目结构

```
.
├── main.py          # 主程序文件
├── pyproject.toml   # 项目配置文件
├── uv.lock         # 依赖锁定文件
├── README.md       # 项目说明
└── output/         # 生成的图像保存目录
```

## 依赖包

- `google-genai`: Google Gemini API客户端
- `Pillow`: 图像处理库