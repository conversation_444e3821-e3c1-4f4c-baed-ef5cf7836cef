#!/usr/bin/env python3
"""
卓建图像生成服务器
基于FastAPI的高性能图像生成服务
"""

import os
import time
import base64
import asyncio
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel, Field, validator
from google import genai
from google.genai import types
from PIL import Image
from io import BytesIO
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
import json
from pathlib import Path

# 创建FastAPI应用
app = FastAPI(
    title="卓建图像生成API",
    description="基于Google Gemini Imagen 3.0的图像生成服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
API_KEY = "sk-sSS1IGDDwqLWnTzY3aB4B4184f0141E19fCe2a272599DcDd"
BASE_URL = "https://aihubmix.com/gemini"
OUTPUT_DIR = "output"
STATIC_DIR = "static"

# 确保目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)

# 数据模型
class ImageGenerationRequest(BaseModel):
    prompt: str
    model: str = "imagen-3.0-generate-002"
    number_of_images: int = 1
    aspect_ratio: str = "1:1"  # "1:1", "9:16", "16:9", "3:4", "4:3"

class ImageGenerationResponse(BaseModel):
    success: bool
    message: str
    images: List[dict] = []
    task_id: Optional[str] = None

class TaskStatus(BaseModel):
    task_id: str
    status: str  # "pending", "processing", "completed", "failed"
    progress: int = 0
    result: Optional[dict] = None

# 任务存储（生产环境建议使用Redis或数据库）
tasks = {}

def create_genai_client():
    """创建Google GenAI客户端"""
    return genai.Client(
        api_key=API_KEY,
        http_options={"base_url": BASE_URL}
    )

def save_image_from_bytes(image_bytes: bytes, filename: str) -> str:
    """保存图像并返回文件路径"""
    image = Image.open(BytesIO(image_bytes))
    file_path = os.path.join(OUTPUT_DIR, filename)
    image.save(file_path)
    return file_path

def generate_images_sync(request: ImageGenerationRequest, task_id: str):
    """同步生成图像"""
    try:
        # 更新任务状态
        tasks[task_id]["status"] = "processing"
        tasks[task_id]["progress"] = 10
        
        # 创建客户端
        client = create_genai_client()
        
        # 更新进度
        tasks[task_id]["progress"] = 30
        
        # 生成图像
        response = client.models.generate_images(
            model=request.model,
            prompt=request.prompt,
            config=types.GenerateImagesConfig(
                number_of_images=request.number_of_images,
                aspect_ratio=request.aspect_ratio,
            )
        )
        
        # 更新进度
        tasks[task_id]["progress"] = 70
        
        # 保存图像
        timestamp = int(time.time())
        saved_images = []
        
        for i, generated_image in enumerate(response.generated_images):
            filename = f"imagen_{timestamp}_{task_id}_{i+1}.png"
            file_path = save_image_from_bytes(generated_image.image.image_bytes, filename)
            
            # 转换为base64用于API响应
            with open(file_path, "rb") as f:
                image_base64 = base64.b64encode(f.read()).decode()
            
            saved_images.append({
                "filename": filename,
                "path": file_path,
                "url": f"/images/{filename}",
                "base64": image_base64
            })
        
        # 完成任务
        tasks[task_id]["status"] = "completed"
        tasks[task_id]["progress"] = 100
        tasks[task_id]["result"] = {
            "success": True,
            "message": f"成功生成 {len(saved_images)} 张图像",
            "images": saved_images
        }
        
    except Exception as e:
        # 任务失败
        tasks[task_id]["status"] = "failed"
        tasks[task_id]["progress"] = 0
        tasks[task_id]["result"] = {
            "success": False,
            "message": f"生成失败: {str(e)}",
            "images": []
        }

@app.get("/", response_class=HTMLResponse)
async def root():
    """返回主页HTML"""
    try:
        with open(os.path.join(STATIC_DIR, "index.html"), "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return """
        <html>
            <body>
                <h1>卓建图像生成API</h1>
                <p>API服务正在运行</p>
                <p><a href="/docs">查看API文档</a></p>
            </body>
        </html>
        """

@app.post("/generate", response_model=ImageGenerationResponse)
async def generate_image(request: ImageGenerationRequest, background_tasks: BackgroundTasks):
    """异步生成图像"""
    # 生成任务ID
    task_id = f"task_{int(time.time())}_{hash(request.prompt) % 10000}"
    
    # 初始化任务
    tasks[task_id] = {
        "status": "pending",
        "progress": 0,
        "created_at": time.time(),
        "request": request.dict()
    }
    
    # 添加后台任务
    background_tasks.add_task(generate_images_sync, request, task_id)
    
    return ImageGenerationResponse(
        success=True,
        message="图像生成任务已启动",
        task_id=task_id
    )

@app.get("/task/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = tasks[task_id]
    return TaskStatus(
        task_id=task_id,
        status=task["status"],
        progress=task["progress"],
        result=task.get("result")
    )

@app.get("/images/{filename}")
async def get_image(filename: str):
    """获取生成的图像"""
    file_path = os.path.join(OUTPUT_DIR, filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="图像不存在")
    return FileResponse(file_path)

@app.get("/tasks")
async def list_tasks():
    """列出所有任务"""
    return {"tasks": tasks}

@app.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """删除任务"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    del tasks[task_id]
    return {"message": "任务已删除"}

# 挂载静态文件
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
