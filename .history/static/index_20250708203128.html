<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卓建图像生成工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            display: none;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ddd;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s;
        }

        .results-section {
            display: none;
            margin-top: 30px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        /* 根据图像数量调整网格 */
        .image-grid[data-count="1"] {
            grid-template-columns: 1fr;
            max-width: 600px;
            margin: 20px auto 0;
        }

        .image-grid[data-count="2"] {
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        }

        .image-grid[data-count="3"] {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .image-grid[data-count="4"] {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        .image-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s;
            display: flex;
            flex-direction: column;
        }

        .image-card:hover {
            transform: translateY(-5px);
        }

        .image-container {
            position: relative;
            width: 100%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .image-card img {
            width: 100%;
            height: auto;
            max-width: 100%;
            object-fit: contain;
            display: block;
            border-radius: 0;
        }

        .image-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-size: 14px;
        }

        .image-error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #dc3545;
            font-size: 14px;
            text-align: center;
        }

        .image-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .image-card:hover .image-overlay {
            opacity: 1;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            max-width: 1200px;
            height: 90%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .modal-image {
            max-width: 100%;
            max-height: 80%;
            object-fit: contain;
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.5);
        }

        .modal-info {
            color: white;
            text-align: center;
            margin-top: 20px;
            font-size: 16px;
        }

        .close {
            position: absolute;
            top: 20px;
            right: 35px;
            color: white;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }

        .close:hover {
            color: #ccc;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .image-info {
            padding: 20px;
        }

        .image-info h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .main-content {
                padding: 20px;
            }

            .image-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .image-grid[data-count="1"],
            .image-grid[data-count="2"],
            .image-grid[data-count="3"],
            .image-grid[data-count="4"] {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                height: 95%;
                padding: 10px;
            }

            .modal-image {
                max-height: 70%;
            }

            .close {
                top: 10px;
                right: 20px;
                font-size: 30px;
            }

            .image-info {
                padding: 15px;
            }

            .download-btn {
                padding: 6px 12px;
                font-size: 14px;
                margin: 5px 5px 0 0;
                display: inline-block;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .form-section {
                padding: 20px;
            }

            .modal-info {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 卓建图像生成工具</h1>
            <p>基于Google Gemini Imagen 3.0 API的智能图像生成平台</p>
        </div>

        <div class="main-content">
            <div class="form-section">
                <h2>图像生成配置</h2>
                <form id="imageForm">
                    <div class="form-group">
                        <label for="prompt">图像描述 (Prompt)</label>
                        <textarea id="prompt" name="prompt" placeholder="请输入您想要生成的图像描述（英文）..." required>A minimalist logo for a LLM router market company on a solid white background. trident in a circle as the main symbol, with ONLY text 'InferEra' below.</textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="model">模型选择</label>
                            <select id="model" name="model">
                                <option value="imagen-3.0-generate-002">Imagen 3.0</option>
                                <option value="imagen-4.0-generate-preview-05-20">Imagen 4.0 (预览)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="aspect_ratio">宽高比</label>
                            <select id="aspect_ratio" name="aspect_ratio">
                                <option value="1:1">1:1 (正方形)</option>
                                <option value="16:9">16:9 (宽屏)</option>
                                <option value="9:16">9:16 (竖屏)</option>
                                <option value="4:3">4:3 (标准)</option>
                                <option value="3:4">3:4 (竖版标准)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="number_of_images">图像数量</label>
                            <select id="number_of_images" name="number_of_images">
                                <option value="1">1张</option>
                                <option value="2">2张</option>
                                <option value="3">3张</option>
                                <option value="4">4张</option>
                            </select>
                        </div>
                    </div>

                    <button type="submit" class="btn" id="generateBtn">
                        🚀 生成图像
                    </button>
                </form>
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <div class="progress-section" id="progressSection">
                <h3>生成进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">准备中...</p>
            </div>

            <div class="results-section" id="resultsSection">
                <h2>生成结果</h2>
                <div class="image-grid" id="imageGrid"></div>
            </div>
        </div>
    </div>

    <!-- 图像模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <div id="modalInfo" class="modal-info"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let currentTaskId = null;
        let pollInterval = null;
        let statsInterval = null;

        // DOM元素
        const form = document.getElementById('imageForm');
        const generateBtn = document.getElementById('generateBtn');
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const resultsSection = document.getElementById('resultsSection');
        const imageGrid = document.getElementById('imageGrid');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // 显示错误消息
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        // 隐藏消息
        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // 更新进度
        function updateProgress(progress, text) {
            progressFill.style.width = progress + '%';
            progressText.textContent = text;
        }

        // 轮询任务状态
        function pollTaskStatus(taskId) {
            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE}/task/${taskId}`);
                    const data = await response.json();

                    updateProgress(data.progress, `状态: ${data.status}`);

                    if (data.status === 'completed') {
                        clearInterval(pollInterval);
                        displayResults(data.result);
                        generateBtn.disabled = false;
                        generateBtn.textContent = '🚀 生成图像';
                        progressSection.style.display = 'none';
                        showSuccess('图像生成完成！');
                    } else if (data.status === 'failed') {
                        clearInterval(pollInterval);
                        showError(data.result?.message || '生成失败');
                        generateBtn.disabled = false;
                        generateBtn.textContent = '🚀 生成图像';
                        progressSection.style.display = 'none';
                    }
                } catch (error) {
                    console.error('轮询错误:', error);
                    clearInterval(pollInterval);
                    showError('获取任务状态失败');
                    generateBtn.disabled = false;
                    generateBtn.textContent = '🚀 生成图像';
                    progressSection.style.display = 'none';
                }
            }, 2000);
        }

        // 显示结果
        function displayResults(result) {
            if (!result.success || !result.images.length) {
                showError(result.message || '没有生成图像');
                return;
            }

            imageGrid.innerHTML = '';

            // 设置网格数量属性以优化布局
            imageGrid.setAttribute('data-count', result.images.length);

            result.images.forEach((image, index) => {
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';

                // 创建图像容器
                const imageContainer = document.createElement('div');
                imageContainer.className = 'image-container';

                // 创建加载提示
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'image-loading';
                loadingDiv.textContent = '加载中...';
                imageContainer.appendChild(loadingDiv);

                // 创建图像元素
                const img = document.createElement('img');
                img.src = image.url;
                img.alt = `生成的图像 ${index + 1}`;
                img.loading = 'lazy';
                img.style.display = 'none'; // 初始隐藏

                // 图像加载成功
                img.onload = function() {
                    loadingDiv.style.display = 'none';
                    img.style.display = 'block';

                    // 添加图像信息覆盖层
                    const overlay = document.createElement('div');
                    overlay.className = 'image-overlay';
                    overlay.textContent = `${img.naturalWidth} × ${img.naturalHeight}`;
                    imageContainer.appendChild(overlay);
                };

                // 图像加载失败
                img.onerror = function() {
                    loadingDiv.style.display = 'none';
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'image-error';
                    errorDiv.innerHTML = '❌<br>图像加载失败';
                    imageContainer.appendChild(errorDiv);
                };

                imageContainer.appendChild(img);

                // 创建信息区域
                const imageInfo = document.createElement('div');
                imageInfo.className = 'image-info';
                imageInfo.innerHTML = `
                    <h3>图像 ${index + 1}</h3>
                    <p>文件名: ${image.filename}</p>
                    <div style="margin-top: 10px;">
                        <a href="${image.url}" download="${image.filename}" class="download-btn" style="margin-right: 10px;">
                            📥 下载图像
                        </a>
                        <button onclick="openImageModal('${image.url}', '${image.filename}')" class="download-btn" style="background: #007bff;">
                            🔍 查看大图
                        </button>
                    </div>
                `;

                imageCard.appendChild(imageContainer);
                imageCard.appendChild(imageInfo);
                imageGrid.appendChild(imageCard);
            });

            resultsSection.style.display = 'block';
        }

        // 表单提交处理
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            hideMessages();
            
            const formData = new FormData(form);
            const requestData = {
                prompt: formData.get('prompt'),
                model: formData.get('model'),
                aspect_ratio: formData.get('aspect_ratio'),
                number_of_images: parseInt(formData.get('number_of_images'))
            };

            try {
                generateBtn.disabled = true;
                generateBtn.textContent = '生成中...';
                progressSection.style.display = 'block';
                updateProgress(0, '提交请求...');

                const response = await fetch(`${API_BASE}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                if (data.success) {
                    currentTaskId = data.task_id;
                    updateProgress(5, '任务已创建，开始生成...');
                    pollTaskStatus(currentTaskId);
                } else {
                    throw new Error(data.message || '请求失败');
                }

            } catch (error) {
                console.error('提交错误:', error);
                showError('提交请求失败: ' + error.message);
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 生成图像';
                progressSection.style.display = 'none';
            }
        });

        // 模态框功能
        function openImageModal(imageUrl, filename) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalInfo = document.getElementById('modalInfo');

            modalImage.src = imageUrl;
            modalImage.alt = filename;

            // 获取图像信息
            modalImage.onload = function() {
                modalInfo.innerHTML = `
                    <h3>${filename}</h3>
                    <p>尺寸: ${this.naturalWidth} × ${this.naturalHeight} 像素</p>
                    <p>文件大小: 正在计算...</p>
                    <div style="margin-top: 15px;">
                        <a href="${imageUrl}" download="${filename}" style="color: #4facfe; text-decoration: none; margin-right: 20px;">
                            📥 下载原图
                        </a>
                        <button onclick="copyImageUrl('${imageUrl}')" style="background: none; border: none; color: #4facfe; cursor: pointer;">
                            📋 复制链接
                        </button>
                    </div>
                `;

                // 获取文件大小
                fetch(imageUrl, { method: 'HEAD' })
                    .then(response => {
                        const size = response.headers.get('content-length');
                        if (size) {
                            const sizeKB = Math.round(size / 1024);
                            const sizeMB = (sizeKB / 1024).toFixed(2);
                            const sizeText = sizeKB > 1024 ? `${sizeMB} MB` : `${sizeKB} KB`;
                            modalInfo.innerHTML = modalInfo.innerHTML.replace('正在计算...', sizeText);
                        }
                    })
                    .catch(() => {
                        modalInfo.innerHTML = modalInfo.innerHTML.replace('正在计算...', '未知');
                    });
            };

            modal.style.display = 'block';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        }

        function copyImageUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                showSuccess('图像链接已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showSuccess('图像链接已复制到剪贴板');
            });
        }

        // 点击模态框背景关闭
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeImageModal();
            }
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
        });
    </script>
</body>
</html>
