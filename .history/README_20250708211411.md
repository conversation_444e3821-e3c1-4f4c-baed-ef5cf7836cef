# 🎨 卓建图像生成服务

基于Google Gemini Imagen API的现代化图像生成平台，提供Web界面和RESTful API。

## ✨ 功能特性

### 🖼️ 图像生成
- 支持Imagen 3.0和4.0模型
- 多种宽高比（1:1, 9:16, 16:9, 3:4, 4:3）
- 批量生成（1-4张图像）
- 高质量图像输出

### 🌐 Web界面
- 现代化响应式设计
- 实时进度显示
- 图像预览和下载
- 大图查看模态框
- 服务状态监控

### 🔌 API服务
- RESTful API接口
- 异步任务处理
- 任务状态查询
- 自动API文档
- 错误处理和日志

## 🚀 快速开始

### 前提条件

- Python 3.12+
- uv包管理器

### 安装依赖

```bash
uv sync
```

### 配置API密钥

**首次使用需要配置API密钥：**

```bash
# 方法1：使用快速修复工具（推荐）
uv run python fix_api_key.py

# 方法2：使用配置助手
uv run python config_helper.py --setup

# 方法3：手动编辑.env文件
# 1. 创建.env文件：uv run python config_helper.py --create-env
# 2. 编辑文件，将 GEMINI_API_KEY=your-api-key-here 改为您的真实API密钥
```

**获取API密钥：**
1. 访问 [https://aihubmix.com](https://aihubmix.com) 注册账号
2. 在控制台生成API密钥
3. 复制密钥（通常以`sk-`开头）

### 启动服务

```bash
# 简单启动
python run.py

# 或者使用开发模式
python run.py --reload

# 自定义端口
python run.py --port 9000
```

### 访问服务

启动后访问以下地址：

- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/api/docs
- **交互式API**: http://localhost:8000/api/redoc

## ⚙️ 配置

### 环境变量

创建 `.env` 文件或设置环境变量：

```bash
# API配置
GEMINI_API_KEY=your-api-key-here
GEMINI_BASE_URL=https://aihubmix.com/gemini

# 服务器配置（可选）
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
```

### 高级配置

编辑 `api_server.py` 中的 `Config` 类：

```python
class Config:
    MAX_CONCURRENT_TASKS = 5  # 最大并发任务数
    TASK_TIMEOUT = 300        # 任务超时时间（秒）
```

## 📁 项目结构

```
.
├── api_server.py    # FastAPI服务器
├── run.py          # 启动脚本
├── static/         # 静态文件
│   └── index.html  # Web界面
├── output/         # 生成的图像
├── pyproject.toml  # 项目配置
└── README.md       # 项目说明
```

## 📡 API接口

### 生成图像

```http
POST /api/generate
Content-Type: application/json

{
  "prompt": "A beautiful landscape",
  "model": "imagen-3.0-generate-002",
  "aspect_ratio": "16:9",
  "number_of_images": 2
}
```

### 查询任务状态

```http
GET /api/task/{task_id}
```

### 获取服务统计

```http
GET /api/stats
```

## 🛠️ 依赖包

- `fastapi`: Web框架
- `uvicorn`: ASGI服务器
- `google-genai`: Google Gemini API客户端
- `Pillow`: 图像处理库
- `pydantic`: 数据验证