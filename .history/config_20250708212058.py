"""
配置文件
包含API密钥、服务器配置等
"""

import os
from typing import Dict, Any

# API配置
API_CONFIG = {
    "api_key": os.getenv("GEMINI_API_KEY", "sk-sSS1IGDDwqLWnTzY3aB4B4184f0141E19fCe2a272599DcDd"),
    "base_url": os.getenv("GEMINI_BASE_URL", "https://aihubmix.com/gemini"),
    "default_model": os.getenv("DEFAULT_MODEL", "imagen-3.0-generate-002"),
    "timeout": int(os.getenv("API_TIMEOUT", "60"))
}

# 服务器配置
SERVER_CONFIG = {
    "host": os.getenv("SERVER_HOST", "0.0.0.0"),
    "port": int(os.getenv("SERVER_PORT", "8000")),
    "reload": os.getenv("SERVER_RELOAD", "true").lower() == "true",
    "log_level": os.getenv("LOG_LEVEL", "info")
}

# 目录配置
DIRECTORY_CONFIG = {
    "output_dir": os.getenv("OUTPUT_DIR", "output"),
    "static_dir": os.getenv("STATIC_DIR", "static"),
    "temp_dir": os.getenv("TEMP_DIR", "temp")
}



# 邮件配置
EMAIL_CONFIG = {
    "smtp_server": os.getenv("SMTP_SERVER", "smtp.gmail.com"),
    "smtp_port": int(os.getenv("SMTP_PORT", "587")),
    "smtp_username": os.getenv("SMTP_USERNAME", ""),
    "smtp_password": os.getenv("SMTP_PASSWORD", ""),
    "from_email": os.getenv("FROM_EMAIL", ""),
    "use_tls": os.getenv("SMTP_USE_TLS", "true").lower() == "true"
}

# 图像生成配置
IMAGE_CONFIG = {
    "supported_models": [
        "imagen-3.0-generate-002",
        "imagen-4.0-generate-preview-05-20"
    ],
    "supported_aspect_ratios": [
        "1:1", "9:16", "16:9", "3:4", "4:3"
    ],
    "max_images_per_request": int(os.getenv("MAX_IMAGES_PER_REQUEST", "4")),
    "max_prompt_length": int(os.getenv("MAX_PROMPT_LENGTH", "1000")),
    "default_aspect_ratio": "1:1",
    "default_number_of_images": 1
}

# 任务配置
TASK_CONFIG = {
    "max_concurrent_tasks": int(os.getenv("MAX_CONCURRENT_TASKS", "5")),
    "task_timeout": int(os.getenv("TASK_TIMEOUT", "300")),  # 5分钟
    "cleanup_interval": int(os.getenv("CLEANUP_INTERVAL", "3600")),  # 1小时
    "max_task_history": int(os.getenv("MAX_TASK_HISTORY", "100"))
}

# 安全配置
SECURITY_CONFIG = {
    "allowed_origins": os.getenv("ALLOWED_ORIGINS", "*").split(","),
    "rate_limit": {
        "requests_per_minute": int(os.getenv("RATE_LIMIT_RPM", "10")),
        "requests_per_hour": int(os.getenv("RATE_LIMIT_RPH", "100"))
    },
    "api_key_header": "X-API-Key",
    "require_api_key": os.getenv("REQUIRE_API_KEY", "false").lower() == "true"
}

def get_config(section: str) -> Dict[str, Any]:
    """获取指定配置节"""
    config_map = {
        "api": API_CONFIG,
        "server": SERVER_CONFIG,
        "directory": DIRECTORY_CONFIG,
        "email": EMAIL_CONFIG,
        "image": IMAGE_CONFIG,
        "task": TASK_CONFIG,
        "security": SECURITY_CONFIG
    }
    return config_map.get(section, {})

def validate_config():
    """验证配置"""
    errors = []
    
    # 验证API配置
    if not API_CONFIG["api_key"]:
        errors.append("API密钥未配置")
    
    # 验证邮件配置（如果启用邮件功能）
    if EMAIL_CONFIG["smtp_username"] and not EMAIL_CONFIG["smtp_password"]:
        errors.append("SMTP密码未配置")
    
    # 验证目录
    for dir_name, dir_path in DIRECTORY_CONFIG.items():
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建目录 {dir_path}: {e}")
    
    return errors

# 环境变量模板
ENV_TEMPLATE = """
# API配置
GEMINI_API_KEY=your-api-key-here
GEMINI_BASE_URL=https://aihubmix.com/gemini
DEFAULT_MODEL=imagen-3.0-generate-002
API_TIMEOUT=60

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
SERVER_RELOAD=true
LOG_LEVEL=info

# 目录配置
OUTPUT_DIR=output
STATIC_DIR=static
TEMP_DIR=temp



# 邮件配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=
SMTP_USE_TLS=true

# 图像生成配置
MAX_IMAGES_PER_REQUEST=4
MAX_PROMPT_LENGTH=1000

# 任务配置
MAX_CONCURRENT_TASKS=5
TASK_TIMEOUT=300
CLEANUP_INTERVAL=3600
MAX_TASK_HISTORY=100

# 安全配置
ALLOWED_ORIGINS=*
RATE_LIMIT_RPM=10
RATE_LIMIT_RPH=100
REQUIRE_API_KEY=false
"""

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
    
    # 显示当前配置
    print("\n当前配置:")
    for section_name in ["api", "server", "directory", "image"]:
        config = get_config(section_name)
        print(f"\n{section_name.upper()}配置:")
        for key, value in config.items():
            if "password" in key.lower() or "key" in key.lower():
                print(f"  {key}: {'*' * len(str(value)) if value else '未设置'}")
            else:
                print(f"  {key}: {value}")
