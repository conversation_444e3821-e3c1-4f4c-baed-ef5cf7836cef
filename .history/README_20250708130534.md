# 卓建图像生成工具

基于Google Gemini Imagen 3.0 API的图像生成工具。

## 功能特性

- 使用Google Gemini Imagen 3.0 API生成高质量图像
- 支持多种宽高比（1:1, 9:16, 16:9, 3:4, 4:3）
- 自动保存生成的图像到output目录
- 使用时间戳避免文件名冲突

## 安装和使用

### 前提条件

- Python 3.12+
- uv包管理器

### 安装依赖

```bash
uv sync
```

### 运行程序

```bash
uv run python main.py
```

或者使用脚本命令：

```bash
uv run zuojian-image
```

## 配置

在使用前，请在`main.py`中替换API密钥：

```python
api_key="your-api-key-here"
```

## 项目结构

```
.
├── main.py          # 主程序文件
├── pyproject.toml   # 项目配置文件
├── uv.lock         # 依赖锁定文件
├── README.md       # 项目说明
└── output/         # 生成的图像保存目录
```

## 依赖包

- `google-genai`: Google Gemini API客户端
- `Pillow`: 图像处理库