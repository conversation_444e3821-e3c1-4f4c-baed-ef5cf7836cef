# 卓建图像生成服务配置文件
# 请根据您的实际情况修改以下配置

# ===========================================
# API配置 (必须配置)
# ===========================================

# Google Gemini API密钥
# 获取方式：
# 1. 访问 https://aihubmix.com 注册账号
# 2. 在控制台生成API密钥
# 3. 将密钥填入下面的配置中
GEMINI_API_KEY=sk-sSS1IGDDwqLWnTzY3aB4B4184f0141E19fCe2a272599DcDd

# API基础URL (通常不需要修改)
GEMINI_BASE_URL=https://aihubmix.com/gemini

# ===========================================
# 服务器配置 (可选)
# ===========================================

# 服务器地址
SERVER_HOST=0.0.0.0

# 服务器端口
SERVER_PORT=8000

# ===========================================
# 高级配置 (可选)
# ===========================================

# 最大并发任务数
MAX_CONCURRENT_TASKS=5

# 任务超时时间（秒）
TASK_TIMEOUT=300

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
