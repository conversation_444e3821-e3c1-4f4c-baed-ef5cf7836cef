#!/bin/bash

# 卓建图像生成服务启动脚本

echo "🎨 卓建图像生成服务启动脚本"
echo "=================================="

# 1. 安装依赖
echo "📦 安装依赖..."
uv sync

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 2. 检查API密钥配置
echo ""
echo "🔑 检查API密钥配置..."

if [ ! -f ".env" ]; then
    echo "⚠️ 未找到.env文件，创建配置文件..."
    uv run python config_helper.py --create-env
fi

# 检查API密钥是否配置
if grep -q "GEMINI_API_KEY=$" .env || grep -q "GEMINI_API_KEY=your-api-key-here" .env; then
    echo "⚠️ API密钥未配置，启动配置工具..."
    uv run python fix_api_key.py
else
    echo "✅ API密钥已配置"
fi

# 3. 启动服务
echo ""
echo "🚀 启动服务..."
python run.py
