# 卓建图像生成 - n8n + HTML 自动化配置

这个配置提供了一个完整的自动化图像生成解决方案，包括Web界面、API服务和n8n工作流。

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTML前端界面   │───▶│   FastAPI服务   │───▶│  Google Gemini  │
│   (用户交互)    │    │   (API处理)     │    │   (图像生成)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   n8n工作流     │    │   文件存储      │
│   (自动化处理)   │    │   (图像保存)    │
└─────────────────┘    └─────────────────┘
```

## 📁 文件结构

```
.
├── api_server.py              # FastAPI服务器
├── config.py                  # 配置文件
├── start_services.py          # 服务启动脚本
├── static/
│   └── index.html            # Web前端界面
├── n8n-workflows/
│   ├── image-generation-workflow.json    # 图像生成工作流
│   └── email-notification-workflow.json  # 邮件通知工作流
├── output/                   # 生成的图像存储目录
└── N8N_SETUP.md             # 本文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
uv sync

# 安装n8n (需要Node.js)
npm install -g n8n
```

### 2. 配置环境

创建 `.env` 文件或设置环境变量：

```bash
# API配置
GEMINI_API_KEY=your-api-key-here
GEMINI_BASE_URL=https://aihubmix.com/gemini

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 邮件配置（可选）
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
```

### 3. 启动服务

```bash
# 启动所有服务（API + n8n）
python start_services.py

# 或者只启动API服务
python start_services.py --skip-n8n

# 开发模式
python start_services.py --dev
```

### 4. 访问界面

- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **n8n界面**: http://localhost:5678

## 🔧 功能特性

### Web界面功能
- ✅ 直观的图像生成表单
- ✅ 实时进度显示
- ✅ 多种模型选择（Imagen 3.0/4.0）
- ✅ 多种宽高比支持
- ✅ 批量图像生成
- ✅ 图像预览和下载
- ✅ 响应式设计

### API功能
- ✅ RESTful API接口
- ✅ 异步任务处理
- ✅ 任务状态查询
- ✅ 图像文件服务
- ✅ CORS支持
- ✅ 自动API文档

### n8n工作流功能
- ✅ Webhook触发器
- ✅ 自动任务轮询
- ✅ 邮件通知
- ✅ 错误处理
- ✅ 结果处理

## 📡 API端点

### 生成图像
```http
POST /generate
Content-Type: application/json

{
  "prompt": "A beautiful landscape",
  "model": "imagen-3.0-generate-002",
  "aspect_ratio": "1:1",
  "number_of_images": 1
}
```

### 查询任务状态
```http
GET /task/{task_id}
```

### 获取图像
```http
GET /images/{filename}
```

### 列出所有任务
```http
GET /tasks
```

## 🔄 n8n工作流配置

### 1. 导入工作流

1. 打开n8n界面 (http://localhost:5678)
2. 点击 "Import from file"
3. 选择 `n8n-workflows/image-generation-workflow.json`
4. 激活工作流

### 2. 配置Webhook

工作流会自动创建Webhook端点：
- **图像生成**: `http://localhost:5678/webhook/webhook-generate`
- **邮件通知**: `http://localhost:5678/webhook/email-notify`

### 3. 使用Webhook

#### 图像生成Webhook
```bash
curl -X POST http://localhost:5678/webhook/webhook-generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over mountains",
    "model": "imagen-3.0-generate-002",
    "aspect_ratio": "16:9",
    "number_of_images": 2
  }'
```

#### 邮件通知Webhook
```bash
curl -X POST http://localhost:5678/webhook/email-notify \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "request": {
      "prompt": "A beautiful sunset",
      "model": "imagen-3.0-generate-002",
      "aspect_ratio": "1:1",
      "number_of_images": 1
    }
  }'
```

## ⚙️ 高级配置

### 自定义配置

编辑 `config.py` 文件来修改：
- API超时设置
- 并发任务限制
- 文件存储路径
- 安全设置

### 邮件通知配置

1. 在n8n中配置邮件凭据
2. 修改邮件模板
3. 设置收件人规则

### 扩展工作流

可以基于现有工作流创建更复杂的自动化：
- 定时批量生成
- 社交媒体发布
- 文件同步到云存储
- 图像质量检查

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的 `GEMINI_API_KEY`
   - 确认密钥有效且有足够配额

2. **n8n连接失败**
   - 确认n8n服务正在运行
   - 检查端口5678是否被占用

3. **图像生成超时**
   - 增加 `API_TIMEOUT` 配置
   - 检查网络连接

4. **邮件发送失败**
   - 检查SMTP配置
   - 确认邮箱开启了应用密码

### 日志查看

```bash
# 查看API服务日志
tail -f api_server.log

# 查看n8n日志
tail -f ~/.n8n/logs/n8n.log
```

## 🔒 安全注意事项

1. **API密钥保护**
   - 不要将API密钥提交到版本控制
   - 使用环境变量存储敏感信息

2. **访问控制**
   - 在生产环境中启用API密钥验证
   - 配置防火墙规则

3. **HTTPS配置**
   - 在生产环境中使用HTTPS
   - 配置SSL证书

## 📈 性能优化

1. **并发控制**
   - 调整 `MAX_CONCURRENT_TASKS`
   - 使用Redis作为任务队列

2. **缓存策略**
   - 实现图像缓存
   - 使用CDN分发静态资源

3. **监控告警**
   - 集成监控系统
   - 设置性能告警

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
