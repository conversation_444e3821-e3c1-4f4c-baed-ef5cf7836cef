# 📖 使用指南

## 🚀 快速开始

### 方法1：一键启动（推荐）

```bash
./start.sh
```

这个脚本会自动：
- 安装依赖
- 检查API密钥配置
- 启动服务

### 方法2：分步执行

```bash
# 1. 安装依赖
uv sync

# 2. 配置API密钥
uv run python setup.py

# 3. 启动服务
python run.py
```

## 🔑 API密钥配置

### 获取API密钥

1. **访问AiHubMix**
   - 打开 [https://aihubmix.com](https://aihubmix.com)
   - 注册账号并登录

2. **生成API密钥**
   - 进入控制台/仪表板
   - 找到API密钥管理
   - 创建新的API密钥
   - 复制生成的密钥

### 配置方法

#### 方法1：使用setup.py（推荐）
```bash
uv run python setup.py
```

#### 方法2：手动编辑.env文件
```bash
# 编辑.env文件
nano .env

# 修改这一行：
GEMINI_API_KEY=your-actual-api-key-here
```

#### 方法3：设置环境变量
```bash
export GEMINI_API_KEY=your-actual-api-key-here
```

## 🌐 访问服务

启动成功后，您可以访问：

- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/api/docs
- **交互式API**: http://localhost:8000/api/redoc

## 🎨 使用Web界面

1. **打开浏览器**，访问 http://localhost:8000

2. **输入提示词**
   - 支持中文和英文
   - 描述您想要生成的图像

3. **选择参数**
   - **模型**: Imagen 3.0 或 4.0
   - **宽高比**: 1:1, 16:9, 9:16, 4:3, 3:4
   - **数量**: 1-4张图像

4. **点击生成**
   - 等待生成完成
   - 查看生成的图像

5. **管理图像**
   - 点击图像查看大图
   - 右键保存图像
   - 使用下载链接

## 🔧 启动选项

```bash
# 基本启动
python run.py

# 开发模式（自动重载）
python run.py --reload

# 自定义端口
python run.py --port 9000

# 自定义主机
python run.py --host 127.0.0.1

# 仅检查环境
python run.py --check-only
```

## 🛠️ 故障排除

### 常见问题

#### 1. API密钥错误
**症状**: `API密钥未配置` 或 `401 Unauthorized`

**解决方案**:
```bash
# 重新配置API密钥
uv run python setup.py

# 或检查配置
uv run python config_helper.py --check
```

#### 2. 端口被占用
**症状**: `Address already in use`

**解决方案**:
```bash
# 使用不同端口
python run.py --port 9000

# 或停止占用进程
lsof -ti:8000 | xargs kill -9
```

#### 3. 依赖问题
**症状**: 模块导入错误

**解决方案**:
```bash
# 重新安装依赖
uv sync --reinstall
```

#### 4. 图像生成失败
**症状**: 生成任务失败

**可能原因**:
- API密钥无效或过期
- 网络连接问题
- 提示词不合适
- 账户余额不足

**解决方案**:
- 检查API密钥是否有效
- 确认网络连接正常
- 尝试简化提示词
- 检查账户余额

### 检查工具

```bash
# 检查配置状态
uv run python config_helper.py --check

# 测试API连接
uv run python config_helper.py --test

# 运行系统测试
uv run python test_optimized.py
```

## 📊 API使用

### 生成图像

```bash
curl -X POST "http://localhost:8000/api/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over mountains",
    "model": "imagen-3.0-generate-002",
    "aspect_ratio": "16:9",
    "number_of_images": 1
  }'
```

### 查询任务状态

```bash
curl "http://localhost:8000/api/task/{task_id}"
```

### 获取服务统计

```bash
curl "http://localhost:8000/api/stats"
```

## 🔒 安全注意事项

1. **保护API密钥**
   - 不要在代码中硬编码API密钥
   - 不要将.env文件提交到版本控制
   - 定期更换API密钥

2. **网络安全**
   - 在生产环境中使用HTTPS
   - 配置适当的防火墙规则
   - 考虑使用反向代理

3. **访问控制**
   - 限制服务访问IP
   - 考虑添加认证机制
   - 监控API使用情况

## 📈 性能优化

1. **并发设置**
   - 调整`MAX_CONCURRENT_TASKS`参数
   - 根据服务器性能设置合适的值

2. **资源管理**
   - 定期清理生成的图像
   - 监控磁盘空间使用

3. **网络优化**
   - 使用CDN加速静态文件
   - 配置适当的缓存策略

## 🎯 最佳实践

1. **提示词编写**
   - 使用清晰、具体的描述
   - 避免过于复杂的提示词
   - 可以参考优秀的提示词示例

2. **参数选择**
   - 根据用途选择合适的宽高比
   - 平衡质量和生成时间
   - 批量生成时注意资源消耗

3. **错误处理**
   - 检查任务状态
   - 处理生成失败的情况
   - 实现重试机制

现在您可以开始使用卓建图像生成服务了！🎨
