<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卓建图像生成工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            display: none;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ddd;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s;
        }

        .results-section {
            display: none;
            margin-top: 30px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .image-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s;
        }

        .image-card:hover {
            transform: translateY(-5px);
        }

        .image-card img {
            width: 100%;
            height: 300px;
            object-fit: cover;
        }

        .image-info {
            padding: 20px;
        }

        .image-info h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 卓建图像生成工具</h1>
            <p>基于Google Gemini Imagen 3.0 API的智能图像生成平台</p>
        </div>

        <div class="main-content">
            <div class="form-section">
                <h2>图像生成配置</h2>
                <form id="imageForm">
                    <div class="form-group">
                        <label for="prompt">图像描述 (Prompt)</label>
                        <textarea id="prompt" name="prompt" placeholder="请输入您想要生成的图像描述（英文）..." required>A minimalist logo for a LLM router market company on a solid white background. trident in a circle as the main symbol, with ONLY text 'InferEra' below.</textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="model">模型选择</label>
                            <select id="model" name="model">
                                <option value="imagen-3.0-generate-002">Imagen 3.0</option>
                                <option value="imagen-4.0-generate-preview-05-20">Imagen 4.0 (预览)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="aspect_ratio">宽高比</label>
                            <select id="aspect_ratio" name="aspect_ratio">
                                <option value="1:1">1:1 (正方形)</option>
                                <option value="16:9">16:9 (宽屏)</option>
                                <option value="9:16">9:16 (竖屏)</option>
                                <option value="4:3">4:3 (标准)</option>
                                <option value="3:4">3:4 (竖版标准)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="number_of_images">图像数量</label>
                            <select id="number_of_images" name="number_of_images">
                                <option value="1">1张</option>
                                <option value="2">2张</option>
                                <option value="3">3张</option>
                                <option value="4">4张</option>
                            </select>
                        </div>
                    </div>

                    <button type="submit" class="btn" id="generateBtn">
                        🚀 生成图像
                    </button>
                </form>
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <div class="progress-section" id="progressSection">
                <h3>生成进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">准备中...</p>
            </div>

            <div class="results-section" id="resultsSection">
                <h2>生成结果</h2>
                <div class="image-grid" id="imageGrid"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '';
        let currentTaskId = null;
        let pollInterval = null;

        // DOM元素
        const form = document.getElementById('imageForm');
        const generateBtn = document.getElementById('generateBtn');
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const resultsSection = document.getElementById('resultsSection');
        const imageGrid = document.getElementById('imageGrid');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // 显示错误消息
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        // 隐藏消息
        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // 更新进度
        function updateProgress(progress, text) {
            progressFill.style.width = progress + '%';
            progressText.textContent = text;
        }

        // 轮询任务状态
        function pollTaskStatus(taskId) {
            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE}/task/${taskId}`);
                    const data = await response.json();

                    updateProgress(data.progress, `状态: ${data.status}`);

                    if (data.status === 'completed') {
                        clearInterval(pollInterval);
                        displayResults(data.result);
                        generateBtn.disabled = false;
                        generateBtn.textContent = '🚀 生成图像';
                        progressSection.style.display = 'none';
                        showSuccess('图像生成完成！');
                    } else if (data.status === 'failed') {
                        clearInterval(pollInterval);
                        showError(data.result?.message || '生成失败');
                        generateBtn.disabled = false;
                        generateBtn.textContent = '🚀 生成图像';
                        progressSection.style.display = 'none';
                    }
                } catch (error) {
                    console.error('轮询错误:', error);
                    clearInterval(pollInterval);
                    showError('获取任务状态失败');
                    generateBtn.disabled = false;
                    generateBtn.textContent = '🚀 生成图像';
                    progressSection.style.display = 'none';
                }
            }, 2000);
        }

        // 显示结果
        function displayResults(result) {
            if (!result.success || !result.images.length) {
                showError(result.message || '没有生成图像');
                return;
            }

            imageGrid.innerHTML = '';
            
            result.images.forEach((image, index) => {
                const imageCard = document.createElement('div');
                imageCard.className = 'image-card';
                
                imageCard.innerHTML = `
                    <img src="${image.url}" alt="生成的图像 ${index + 1}" loading="lazy">
                    <div class="image-info">
                        <h3>图像 ${index + 1}</h3>
                        <p>文件名: ${image.filename}</p>
                        <a href="${image.url}" download="${image.filename}" class="download-btn">
                            📥 下载图像
                        </a>
                    </div>
                `;
                
                imageGrid.appendChild(imageCard);
            });

            resultsSection.style.display = 'block';
        }

        // 表单提交处理
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            hideMessages();
            
            const formData = new FormData(form);
            const requestData = {
                prompt: formData.get('prompt'),
                model: formData.get('model'),
                aspect_ratio: formData.get('aspect_ratio'),
                number_of_images: parseInt(formData.get('number_of_images'))
            };

            try {
                generateBtn.disabled = true;
                generateBtn.textContent = '生成中...';
                progressSection.style.display = 'block';
                updateProgress(0, '提交请求...');

                const response = await fetch(`${API_BASE}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                if (data.success) {
                    currentTaskId = data.task_id;
                    updateProgress(5, '任务已创建，开始生成...');
                    pollTaskStatus(currentTaskId);
                } else {
                    throw new Error(data.message || '请求失败');
                }

            } catch (error) {
                console.error('提交错误:', error);
                showError('提交请求失败: ' + error.message);
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 生成图像';
                progressSection.style.display = 'none';
            }
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
        });
    </script>
</body>
</html>
