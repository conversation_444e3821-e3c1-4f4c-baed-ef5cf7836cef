[project]
name = "zuojian-image"
version = "0.1.0"
description = "基于FastAPI和HTML的现代化图像生成服务，使用Google Gemini Imagen API"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "google-genai>=1.24.0",
    "Pillow>=11.3.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "requests>=2.31.0",
    "pydantic>=2.5.0",
    "jinja2>=3.1.0",
    "python-dotenv>=1.0.0",
]

[project.scripts]
zuojian-image = "run:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.uv]
dev-dependencies = []
