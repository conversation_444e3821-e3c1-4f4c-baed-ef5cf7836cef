#!/usr/bin/env python3
"""
服务启动脚本
启动API服务器和相关服务
"""

import os
import sys
import subprocess
import time
import signal
import argparse
from pathlib import Path
from config import get_config, validate_config

class ServiceManager:
    def __init__(self):
        self.processes = {}
        self.running = True
        
    def start_api_server(self):
        """启动API服务器"""
        print("🚀 启动API服务器...")
        server_config = get_config("server")
        
        cmd = [
            sys.executable, "-m", "uvicorn",
            "api_server:app",
            "--host", server_config["host"],
            "--port", str(server_config["port"]),
            "--log-level", server_config["log_level"]
        ]
        
        if server_config["reload"]:
            cmd.append("--reload")
        
        process = subprocess.Popen(cmd)
        self.processes["api_server"] = process
        print(f"✅ API服务器已启动 (PID: {process.pid})")
        return process
    
    def check_n8n_status(self):
        """检查n8n服务状态"""
        try:
            import requests
            n8n_config = get_config("n8n")
            response = requests.get(f"{n8n_config['api_base_url']}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_n8n_if_needed(self):
        """如果需要，启动n8n服务"""
        if self.check_n8n_status():
            print("✅ n8n服务已运行")
            return None
        
        print("🔄 尝试启动n8n服务...")
        try:
            # 尝试使用npx启动n8n
            cmd = ["npx", "n8n", "start"]
            process = subprocess.Popen(cmd, 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
            self.processes["n8n"] = process
            print(f"✅ n8n服务已启动 (PID: {process.pid})")
            
            # 等待n8n启动
            for i in range(30):  # 等待最多30秒
                if self.check_n8n_status():
                    print("✅ n8n服务启动成功")
                    break
                time.sleep(1)
                print(f"⏳ 等待n8n启动... ({i+1}/30)")
            else:
                print("⚠️ n8n服务启动超时，请手动检查")
            
            return process
        except FileNotFoundError:
            print("⚠️ 未找到n8n，请先安装: npm install -g n8n")
            return None
        except Exception as e:
            print(f"❌ 启动n8n失败: {e}")
            return None
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n📡 收到信号 {signum}，正在关闭服务...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """关闭所有服务"""
        print("🛑 正在关闭服务...")
        self.running = False
        
        for service_name, process in self.processes.items():
            if process and process.poll() is None:
                print(f"🔄 关闭 {service_name}...")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                    print(f"✅ {service_name} 已关闭")
                except subprocess.TimeoutExpired:
                    print(f"⚠️ 强制关闭 {service_name}...")
                    process.kill()
                    process.wait()
                except Exception as e:
                    print(f"❌ 关闭 {service_name} 失败: {e}")
    
    def monitor_services(self):
        """监控服务状态"""
        print("👀 开始监控服务状态...")
        
        while self.running:
            try:
                # 检查API服务器
                api_process = self.processes.get("api_server")
                if api_process and api_process.poll() is not None:
                    print("❌ API服务器已停止，重新启动...")
                    self.start_api_server()
                
                # 检查n8n服务
                n8n_process = self.processes.get("n8n")
                if n8n_process and n8n_process.poll() is not None:
                    print("❌ n8n服务已停止，重新启动...")
                    self.start_n8n_if_needed()
                
                time.sleep(10)  # 每10秒检查一次
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"⚠️ 监控过程中出错: {e}")
                time.sleep(5)
    
    def install_dependencies(self):
        """安装依赖"""
        print("📦 检查并安装依赖...")
        
        # 检查Python依赖
        try:
            import fastapi, uvicorn, requests
            print("✅ Python依赖已安装")
        except ImportError as e:
            print(f"❌ 缺少Python依赖: {e}")
            print("请运行: uv add fastapi uvicorn requests")
            return False
        
        # 检查n8n
        try:
            result = subprocess.run(["npx", "n8n", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ n8n已安装: {result.stdout.strip()}")
            else:
                print("⚠️ n8n未正确安装")
                return False
        except (FileNotFoundError, subprocess.TimeoutExpired):
            print("❌ n8n未安装，请运行: npm install -g n8n")
            return False
        
        return True
    
    def create_env_file(self):
        """创建环境变量文件"""
        env_file = Path(".env")
        if not env_file.exists():
            print("📝 创建环境变量文件...")
            from config import ENV_TEMPLATE
            with open(env_file, "w", encoding="utf-8") as f:
                f.write(ENV_TEMPLATE)
            print("✅ 已创建 .env 文件，请根据需要修改配置")
        else:
            print("✅ 环境变量文件已存在")
    
    def run(self, skip_n8n=False, dev_mode=False):
        """运行服务管理器"""
        print("🎯 卓建图像生成服务管理器")
        print("=" * 50)
        
        # 验证配置
        print("🔍 验证配置...")
        errors = validate_config()
        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        print("✅ 配置验证通过")
        
        # 创建环境文件
        self.create_env_file()
        
        # 安装依赖
        if not self.install_dependencies():
            return False
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        try:
            # 启动API服务器
            self.start_api_server()
            
            # 启动n8n（如果需要）
            if not skip_n8n:
                self.start_n8n_if_needed()
            
            # 显示服务信息
            self.show_service_info()
            
            # 开发模式下不监控
            if not dev_mode:
                self.monitor_services()
            else:
                print("🔧 开发模式：不启用服务监控")
                try:
                    while self.running:
                        time.sleep(1)
                except KeyboardInterrupt:
                    pass
        
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
        finally:
            self.shutdown()
        
        return True
    
    def show_service_info(self):
        """显示服务信息"""
        server_config = get_config("server")
        n8n_config = get_config("n8n")
        
        print("\n🌐 服务信息:")
        print(f"  📡 API服务器: http://{server_config['host']}:{server_config['port']}")
        print(f"  📚 API文档: http://{server_config['host']}:{server_config['port']}/docs")
        print(f"  🎨 Web界面: http://{server_config['host']}:{server_config['port']}")
        
        if "n8n" in self.processes:
            print(f"  🔄 n8n界面: http://localhost:5678")
            print(f"  🪝 Webhook: {n8n_config['webhook_base_url']}")
        
        print("\n📋 可用的API端点:")
        print("  POST /generate - 生成图像")
        print("  GET  /task/{task_id} - 查询任务状态")
        print("  GET  /images/{filename} - 获取图像")
        print("  GET  /tasks - 列出所有任务")
        
        print("\n⌨️ 按 Ctrl+C 停止服务")
        print("=" * 50)

def main():
    parser = argparse.ArgumentParser(description="卓建图像生成服务管理器")
    parser.add_argument("--skip-n8n", action="store_true", help="跳过n8n服务启动")
    parser.add_argument("--dev", action="store_true", help="开发模式（不监控服务）")
    parser.add_argument("--config-only", action="store_true", help="仅验证配置")
    
    args = parser.parse_args()
    
    if args.config_only:
        from config import validate_config
        errors = validate_config()
        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            sys.exit(1)
        else:
            print("✅ 配置验证通过")
            sys.exit(0)
    
    manager = ServiceManager()
    success = manager.run(skip_n8n=args.skip_n8n, dev_mode=args.dev)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
